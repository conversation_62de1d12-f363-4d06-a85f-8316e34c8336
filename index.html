<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creator's Toolkit - Aspect Ratio Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-tools"></i> Creator's Toolkit</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active" data-tool="aspect-ratio">
                    <a href="#" class="nav-link">
                        <i class="fas fa-expand-arrows-alt"></i>
                        <span>Aspect Ratio Calculator</span>
                    </a>
                </li>
                <li class="nav-item disabled" data-tool="color-palette">
                    <a href="#" class="nav-link">
                        <i class="fas fa-palette"></i>
                        <span>Color Palette (Coming Soon)</span>
                    </a>
                </li>
                <li class="nav-item disabled" data-tool="image-resizer">
                    <a href="#" class="nav-link">
                        <i class="fas fa-image"></i>
                        <span>Image Resizer (Coming Soon)</span>
                    </a>
                </li>
                <li class="nav-item disabled" data-tool="text-generator">
                    <a href="#" class="nav-link">
                        <i class="fas fa-font"></i>
                        <span>Text Generator (Coming Soon)</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <header class="content-header">
                <h1 id="tool-title">Aspect Ratio Calculator</h1>
                <p id="tool-description">Calculate dimensions based on aspect ratios for your creative projects</p>
            </header>

            <!-- Aspect Ratio Calculator Tool -->
            <div id="aspect-ratio-tool" class="tool-container active">
                <div class="calculator-grid">
                    <!-- Aspect Ratio Selection -->
                    <div class="card">
                        <h3><i class="fas fa-cog"></i> Aspect Ratio</h3>
                        <div class="ratio-buttons">
                            <button class="ratio-btn active" data-ratio="16:9">16:9</button>
                            <button class="ratio-btn" data-ratio="4:3">4:3</button>
                            <button class="ratio-btn" data-ratio="1:1">1:1</button>
                            <button class="ratio-btn" data-ratio="3:2">3:2</button>
                            <button class="ratio-btn" data-ratio="21:9">21:9</button>
                            <button class="ratio-btn" data-ratio="9:16">9:16</button>
                        </div>
                        <div class="custom-ratio">
                            <label>Custom Ratio:</label>
                            <div class="ratio-input">
                                <input type="number" id="custom-width-ratio" placeholder="W" min="1">
                                <span>:</span>
                                <input type="number" id="custom-height-ratio" placeholder="H" min="1">
                                <button id="apply-custom-ratio">Apply</button>
                            </div>
                        </div>
                    </div>

                    <!-- Dimension Calculator -->
                    <div class="card">
                        <h3><i class="fas fa-calculator"></i> Calculate Dimensions</h3>
                        <div class="dimension-inputs">
                            <div class="input-group">
                                <label for="width-input">Width (px)</label>
                                <input type="number" id="width-input" placeholder="Enter width" min="1">
                            </div>
                            <div class="input-group">
                                <label for="height-input">Height (px)</label>
                                <input type="number" id="height-input" placeholder="Enter height" min="1">
                            </div>
                        </div>
                        <div class="calculation-result">
                            <div class="result-display">
                                <h4>Current Ratio: <span id="current-ratio">16:9</span></h4>
                                <div class="dimensions">
                                    <div class="dimension">
                                        <span class="label">Width:</span>
                                        <span id="calculated-width" class="value">-</span>
                                    </div>
                                    <div class="dimension">
                                        <span class="label">Height:</span>
                                        <span id="calculated-height" class="value">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Visual Preview -->
                    <div class="card">
                        <h3><i class="fas fa-eye"></i> Visual Preview</h3>
                        <div class="preview-container">
                            <div id="aspect-preview" class="aspect-preview">
                                <div class="preview-dimensions">
                                    <span id="preview-text">16:9</span>
                                </div>
                            </div>
                            <div class="preview-info">
                                <p>Preview shows the aspect ratio proportions</p>
                            </div>
                        </div>
                    </div>

                    <!-- Common Use Cases -->
                    <div class="card">
                        <h3><i class="fas fa-lightbulb"></i> Common Use Cases</h3>
                        <div class="use-cases">
                            <div class="use-case" data-ratio="16:9">
                                <strong>16:9</strong> - YouTube videos, monitors, TVs
                            </div>
                            <div class="use-case" data-ratio="4:3">
                                <strong>4:3</strong> - Traditional photos, presentations
                            </div>
                            <div class="use-case" data-ratio="1:1">
                                <strong>1:1</strong> - Instagram posts, profile pictures
                            </div>
                            <div class="use-case" data-ratio="9:16">
                                <strong>9:16</strong> - Instagram stories, TikTok, mobile
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Placeholder for future tools -->
            <div id="color-palette-tool" class="tool-container">
                <div class="coming-soon">
                    <i class="fas fa-palette"></i>
                    <h3>Color Palette Generator</h3>
                    <p>Coming soon! This tool will help you generate beautiful color palettes for your projects.</p>
                </div>
            </div>

            <div id="image-resizer-tool" class="tool-container">
                <div class="coming-soon">
                    <i class="fas fa-image"></i>
                    <h3>Image Resizer</h3>
                    <p>Coming soon! Batch resize images while maintaining aspect ratios.</p>
                </div>
            </div>

            <div id="text-generator-tool" class="tool-container">
                <div class="coming-soon">
                    <i class="fas fa-font"></i>
                    <h3>Text Generator</h3>
                    <p>Coming soon! Generate placeholder text and creative content.</p>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
