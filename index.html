<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creator's Toolkit - Aspect Ratio Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-tools"></i> Creator's Toolkit</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active" data-tool="aspect-ratio">
                    <a href="#" class="nav-link">
                        <i class="fas fa-expand-arrows-alt"></i>
                        <span>Aspect Ratio Calculator</span>
                    </a>
                </li>
                <li class="nav-item disabled" data-tool="color-palette">
                    <a href="#" class="nav-link">
                        <i class="fas fa-palette"></i>
                        <span>Color Palette (Coming Soon)</span>
                    </a>
                </li>
                <li class="nav-item disabled" data-tool="image-resizer">
                    <a href="#" class="nav-link">
                        <i class="fas fa-image"></i>
                        <span>Image Resizer (Coming Soon)</span>
                    </a>
                </li>
                <li class="nav-item disabled" data-tool="text-generator">
                    <a href="#" class="nav-link">
                        <i class="fas fa-font"></i>
                        <span>Text Generator (Coming Soon)</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <header class="content-header">
                <h1 id="tool-title">Aspect Ratio Calculator</h1>
                <p id="tool-description">Calculate dimensions based on aspect ratios for your creative projects</p>
            </header>

            <!-- Aspect Ratio Calculator Tool -->
            <div id="aspect-ratio-tool" class="tool-container active">
                <div class="calculator-container">
                    <!-- Main Calculator Section -->
                    <div class="main-calculator">
                        <div class="calculator-card">
                            <h3><i class="fas fa-calculator"></i> Aspect Ratio Calculator</h3>

                            <!-- Ratio Selection -->
                            <div class="section">
                                <h4>Select Aspect Ratio</h4>
                                <div class="ratio-buttons">
                                    <button class="ratio-btn active" data-ratio="16:9">16:9</button>
                                    <button class="ratio-btn" data-ratio="4:3">4:3</button>
                                    <button class="ratio-btn" data-ratio="1:1">1:1</button>
                                    <button class="ratio-btn" data-ratio="3:2">3:2</button>
                                    <button class="ratio-btn" data-ratio="21:9">21:9</button>
                                    <button class="ratio-btn" data-ratio="9:16">9:16</button>
                                </div>

                                <div class="custom-ratio-section">
                                    <label>Or enter custom ratio:</label>
                                    <div class="custom-ratio-inputs">
                                        <input type="number" id="custom-width-ratio" placeholder="16" min="1">
                                        <span class="ratio-separator">:</span>
                                        <input type="number" id="custom-height-ratio" placeholder="9" min="1">
                                        <button id="apply-custom-ratio" class="apply-btn">Apply</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Dimension Inputs -->
                            <div class="section">
                                <h4>Calculate Dimensions</h4>
                                <div class="dimension-calculator">
                                    <div class="input-row">
                                        <div class="input-field">
                                            <label for="width-input">Width</label>
                                            <div class="input-with-unit">
                                                <input type="number" id="width-input" placeholder="1920" min="1">
                                                <span class="unit">px</span>
                                            </div>
                                        </div>
                                        <div class="calculation-arrow">
                                            <i class="fas fa-arrows-alt-h"></i>
                                        </div>
                                        <div class="input-field">
                                            <label for="height-input">Height</label>
                                            <div class="input-with-unit">
                                                <input type="number" id="height-input" placeholder="1080" min="1">
                                                <span class="unit">px</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Results -->
                            <div class="section">
                                <h4>Result</h4>
                                <div class="result-panel">
                                    <div class="current-ratio">
                                        <span class="ratio-label">Current Ratio:</span>
                                        <span id="current-ratio" class="ratio-value">16:9</span>
                                    </div>
                                    <div class="calculated-dimensions">
                                        <div class="dimension-result">
                                            <span class="dim-label">Width:</span>
                                            <span id="calculated-width" class="dim-value">-</span>
                                        </div>
                                        <div class="dimension-result">
                                            <span class="dim-label">Height:</span>
                                            <span id="calculated-height" class="dim-value">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Side Panel -->
                    <div class="side-panel">
                        <!-- Visual Preview -->
                        <div class="preview-card">
                            <h4><i class="fas fa-eye"></i> Preview</h4>
                            <div class="aspect-preview-container">
                                <div id="aspect-preview" class="aspect-preview">
                                    <span id="preview-text">16:9</span>
                                </div>
                            </div>
                        </div>

                        <!-- Common Use Cases -->
                        <div class="use-cases-card">
                            <h4><i class="fas fa-lightbulb"></i> Common Uses</h4>
                            <div class="use-cases-list">
                                <div class="use-case-item" data-ratio="16:9">
                                    <strong>16:9</strong>
                                    <span>YouTube, TV, Monitors</span>
                                </div>
                                <div class="use-case-item" data-ratio="4:3">
                                    <strong>4:3</strong>
                                    <span>Photos, Presentations</span>
                                </div>
                                <div class="use-case-item" data-ratio="1:1">
                                    <strong>1:1</strong>
                                    <span>Instagram, Avatars</span>
                                </div>
                                <div class="use-case-item" data-ratio="9:16">
                                    <strong>9:16</strong>
                                    <span>Stories, TikTok</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Placeholder for future tools -->
            <div id="color-palette-tool" class="tool-container">
                <div class="coming-soon">
                    <i class="fas fa-palette"></i>
                    <h3>Color Palette Generator</h3>
                    <p>Coming soon! This tool will help you generate beautiful color palettes for your projects.</p>
                </div>
            </div>

            <div id="image-resizer-tool" class="tool-container">
                <div class="coming-soon">
                    <i class="fas fa-image"></i>
                    <h3>Image Resizer</h3>
                    <p>Coming soon! Batch resize images while maintaining aspect ratios.</p>
                </div>
            </div>

            <div id="text-generator-tool" class="tool-container">
                <div class="coming-soon">
                    <i class="fas fa-font"></i>
                    <h3>Text Generator</h3>
                    <p>Coming soon! Generate placeholder text and creative content.</p>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
