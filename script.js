// Aspect Ratio Calculator App
class AspectRatioCalculator {
    constructor() {
        this.currentRatio = { width: 16, height: 9 };
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateDisplay();
    }

    bindEvents() {
        // Ratio button events
        document.querySelectorAll('.ratio-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectRatio(e.target);
            });
        });

        // Custom ratio events
        document.getElementById('apply-custom-ratio').addEventListener('click', () => {
            this.applyCustomRatio();
        });

        // Input events for real-time calculation
        document.getElementById('width-input').addEventListener('input', (e) => {
            this.calculateFromWidth(e.target.value);
        });

        document.getElementById('height-input').addEventListener('input', (e) => {
            this.calculateFromHeight(e.target.value);
        });

        // Use case click events
        document.querySelectorAll('.use-case').forEach(useCase => {
            useCase.addEventListener('click', (e) => {
                const ratio = e.target.dataset.ratio;
                if (ratio) {
                    this.selectRatioByString(ratio);
                }
            });
        });

        // Navigation events
        document.querySelectorAll('.nav-item:not(.disabled)').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTool(item.dataset.tool);
            });
        });
    }

    selectRatio(button) {
        // Remove active class from all buttons
        document.querySelectorAll('.ratio-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Add active class to clicked button
        button.classList.add('active');

        // Parse ratio
        const ratio = button.dataset.ratio;
        this.selectRatioByString(ratio);
    }

    selectRatioByString(ratioString) {
        const [width, height] = ratioString.split(':').map(Number);
        this.currentRatio = { width, height };
        this.updateDisplay();
        this.recalculate();

        // Update button selection
        document.querySelectorAll('.ratio-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.ratio === ratioString) {
                btn.classList.add('active');
            }
        });
    }

    applyCustomRatio() {
        const widthRatio = parseInt(document.getElementById('custom-width-ratio').value);
        const heightRatio = parseInt(document.getElementById('custom-height-ratio').value);

        if (widthRatio > 0 && heightRatio > 0) {
            this.currentRatio = { width: widthRatio, height: heightRatio };
            
            // Remove active class from preset buttons
            document.querySelectorAll('.ratio-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            this.updateDisplay();
            this.recalculate();
        } else {
            alert('Please enter valid positive numbers for both width and height ratios.');
        }
    }

    calculateFromWidth(width) {
        if (width && width > 0) {
            const calculatedHeight = Math.round((width * this.currentRatio.height) / this.currentRatio.width);
            document.getElementById('height-input').value = calculatedHeight;
            this.updateCalculatedValues(width, calculatedHeight);
        } else {
            this.clearCalculatedValues();
        }
    }

    calculateFromHeight(height) {
        if (height && height > 0) {
            const calculatedWidth = Math.round((height * this.currentRatio.width) / this.currentRatio.height);
            document.getElementById('width-input').value = calculatedWidth;
            this.updateCalculatedValues(calculatedWidth, height);
        } else {
            this.clearCalculatedValues();
        }
    }

    recalculate() {
        const width = document.getElementById('width-input').value;
        const height = document.getElementById('height-input').value;

        if (width && width > 0) {
            this.calculateFromWidth(width);
        } else if (height && height > 0) {
            this.calculateFromHeight(height);
        }
    }

    updateCalculatedValues(width, height) {
        document.getElementById('calculated-width').textContent = `${width}px`;
        document.getElementById('calculated-height').textContent = `${height}px`;
    }

    clearCalculatedValues() {
        document.getElementById('calculated-width').textContent = '-';
        document.getElementById('calculated-height').textContent = '-';
    }

    updateDisplay() {
        const ratioString = `${this.currentRatio.width}:${this.currentRatio.height}`;
        document.getElementById('current-ratio').textContent = ratioString;
        document.getElementById('preview-text').textContent = ratioString;
        
        // Update preview aspect ratio
        this.updatePreview();
    }

    updatePreview() {
        const preview = document.getElementById('aspect-preview');
        const aspectRatio = this.currentRatio.width / this.currentRatio.height;
        
        // Calculate preview dimensions while maintaining aspect ratio
        const maxWidth = 200;
        const maxHeight = 120;
        
        let previewWidth, previewHeight;
        
        if (aspectRatio > maxWidth / maxHeight) {
            // Width is the limiting factor
            previewWidth = maxWidth;
            previewHeight = maxWidth / aspectRatio;
        } else {
            // Height is the limiting factor
            previewHeight = maxHeight;
            previewWidth = maxHeight * aspectRatio;
        }
        
        preview.style.width = `${previewWidth}px`;
        preview.style.height = `${previewHeight}px`;
    }

    switchTool(toolName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tool="${toolName}"]`).classList.add('active');

        // Hide all tools
        document.querySelectorAll('.tool-container').forEach(container => {
            container.classList.remove('active');
        });

        // Show selected tool
        document.getElementById(`${toolName}-tool`).classList.add('active');

        // Update header
        this.updateToolHeader(toolName);
    }

    updateToolHeader(toolName) {
        const toolInfo = {
            'aspect-ratio': {
                title: 'Aspect Ratio Calculator',
                description: 'Calculate dimensions based on aspect ratios for your creative projects'
            },
            'color-palette': {
                title: 'Color Palette Generator',
                description: 'Generate beautiful color palettes for your creative projects'
            },
            'image-resizer': {
                title: 'Image Resizer',
                description: 'Batch resize images while maintaining aspect ratios'
            },
            'text-generator': {
                title: 'Text Generator',
                description: 'Generate placeholder text and creative content'
            }
        };

        const info = toolInfo[toolName];
        if (info) {
            document.getElementById('tool-title').textContent = info.title;
            document.getElementById('tool-description').textContent = info.description;
        }
    }
}

// Utility functions
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy: ', err);
    });
}

function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #667eea;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AspectRatioCalculator();
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + C to copy current dimensions
        if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
            const width = document.getElementById('calculated-width').textContent;
            const height = document.getElementById('calculated-height').textContent;
            
            if (width !== '-' && height !== '-') {
                const dimensions = `${width} × ${height}`;
                copyToClipboard(dimensions);
                e.preventDefault();
            }
        }
    });
});

// Add some helpful utility functions for future tools
const Utils = {
    // Generate random color
    randomColor: () => {
        return '#' + Math.floor(Math.random()*16777215).toString(16);
    },
    
    // Convert hex to RGB
    hexToRgb: (hex) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    },
    
    // Format file size
    formatFileSize: (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};
