/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* App Container */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Navigation */
.sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar-header h2 {
    color: #4a5568;
    font-size: 1.5rem;
    font-weight: 600;
}

.sidebar-header i {
    margin-right: 0.5rem;
    color: #667eea;
}

.nav-menu {
    list-style: none;
    padding: 1rem 0;
}

.nav-item {
    margin: 0.5rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: #4a5568;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    border-left-color: #667eea;
}

.nav-item.active .nav-link {
    background: rgba(102, 126, 234, 0.15);
    border-left-color: #667eea;
    color: #667eea;
    font-weight: 600;
}

.nav-item.disabled .nav-link {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-link i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    overflow-y: auto;
}

.content-header {
    margin-bottom: 2rem;
    text-align: center;
}

.content-header h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.content-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
}

/* Tool Containers */
.tool-container {
    display: none;
}

.tool-container.active {
    display: block;
}

/* Calculator Container */
.calculator-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Main Calculator */
.main-calculator {
    display: flex;
    flex-direction: column;
}

.calculator-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.calculator-card h3 {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
}

.calculator-card h3 i {
    margin-right: 0.75rem;
    color: #667eea;
    font-size: 1.25rem;
}

/* Sections */
.section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section h4 {
    color: #4a5568;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Ratio Buttons */
.ratio-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.ratio-btn {
    padding: 1rem;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-align: center;
}

.ratio-btn:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.ratio-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Custom Ratio Section */
.custom-ratio-section {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.custom-ratio-section label {
    display: block;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #4a5568;
    font-size: 0.95rem;
}

.custom-ratio-inputs {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    justify-content: center;
}

.custom-ratio-inputs input {
    width: 80px;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    text-align: center;
    font-size: 1rem;
    font-weight: 600;
    transition: border-color 0.3s ease;
}

.custom-ratio-inputs input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ratio-separator {
    font-weight: bold;
    color: #4a5568;
    font-size: 1.2rem;
}

.apply-btn {
    padding: 0.75rem 1.5rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.apply-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Dimension Calculator */
.dimension-calculator {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.input-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
}

.input-field {
    flex: 1;
    max-width: 200px;
}

.input-field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #4a5568;
    text-align: center;
    font-size: 0.95rem;
}

.input-with-unit {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-unit input {
    width: 100%;
    padding: 1rem 2.5rem 1rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
}

.input-with-unit input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-with-unit .unit {
    position: absolute;
    right: 1rem;
    color: #718096;
    font-weight: 600;
    font-size: 0.9rem;
}

.calculation-arrow {
    color: #667eea;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Result Panel */
.result-panel {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.current-ratio {
    text-align: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.ratio-label {
    display: block;
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 0.5rem;
}

.ratio-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.calculated-dimensions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.dimension-result {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dim-label {
    display: block;
    font-size: 0.85rem;
    color: #718096;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.dim-value {
    display: block;
    font-size: 1.3rem;
    font-weight: 700;
    color: #2d3748;
}

/* Side Panel */
.side-panel {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.preview-card, .use-cases-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.preview-card h4, .use-cases-card h4 {
    color: #4a5568;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.preview-card h4 i, .use-cases-card h4 i {
    margin-right: 0.5rem;
    color: #667eea;
}

/* Visual Preview */
.aspect-preview-container {
    text-align: center;
}

.aspect-preview {
    width: 100%;
    max-width: 250px;
    height: 140px;
    margin: 0 auto;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.aspect-preview span {
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Use Cases */
.use-cases-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.use-case-item {
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.use-case-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.use-case-item strong {
    display: block;
    color: #667eea;
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.use-case-item span {
    color: #718096;
    font-size: 0.85rem;
}

/* Coming Soon */
.coming-soon {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.coming-soon i {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.coming-soon h3 {
    color: #4a5568;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.coming-soon p {
    color: #718096;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .calculator-container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .side-panel {
        flex-direction: row;
    }

    .preview-card, .use-cases-card {
        flex: 1;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .main-content {
        margin-left: 0;
        padding: 1rem;
    }

    .calculator-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .calculator-card {
        padding: 1.5rem;
    }

    .input-row {
        flex-direction: column;
        gap: 1rem;
    }

    .calculation-arrow {
        transform: rotate(90deg);
    }

    .calculated-dimensions {
        grid-template-columns: 1fr;
    }

    .ratio-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .custom-ratio-inputs {
        flex-wrap: wrap;
        justify-content: center;
    }

    .side-panel {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 0.5rem;
    }

    .calculator-card {
        padding: 1rem;
        border-radius: 12px;
    }

    .content-header h1 {
        font-size: 2rem;
    }

    .ratio-buttons {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .custom-ratio-inputs {
        gap: 0.5rem;
    }

    .custom-ratio-inputs input {
        width: 60px;
    }
}
