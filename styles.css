/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* App Container */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Navigation */
.sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar-header h2 {
    color: #4a5568;
    font-size: 1.5rem;
    font-weight: 600;
}

.sidebar-header i {
    margin-right: 0.5rem;
    color: #667eea;
}

.nav-menu {
    list-style: none;
    padding: 1rem 0;
}

.nav-item {
    margin: 0.5rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: #4a5568;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    border-left-color: #667eea;
}

.nav-item.active .nav-link {
    background: rgba(102, 126, 234, 0.15);
    border-left-color: #667eea;
    color: #667eea;
    font-weight: 600;
}

.nav-item.disabled .nav-link {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-link i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    overflow-y: auto;
}

.content-header {
    margin-bottom: 2rem;
    text-align: center;
}

.content-header h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.content-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
}

/* Tool Containers */
.tool-container {
    display: none;
}

.tool-container.active {
    display: block;
}

/* Calculator Grid */
.calculator-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card h3 {
    color: #4a5568;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.card h3 i {
    margin-right: 0.5rem;
    color: #667eea;
}

/* Ratio Buttons */
.ratio-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.ratio-btn {
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.ratio-btn:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.ratio-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Custom Ratio */
.custom-ratio {
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
}

.custom-ratio label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #4a5568;
}

.ratio-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ratio-input input {
    width: 60px;
    padding: 0.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    text-align: center;
}

.ratio-input span {
    font-weight: bold;
    color: #4a5568;
}

.ratio-input button {
    padding: 0.5rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.3s ease;
}

.ratio-input button:hover {
    background: #5a67d8;
}

/* Dimension Inputs */
.dimension-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #4a5568;
}

.input-group input {
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Calculation Result */
.result-display {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    padding: 1rem;
}

.result-display h4 {
    color: #4a5568;
    margin-bottom: 0.75rem;
    text-align: center;
}

.dimensions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.dimension {
    text-align: center;
    padding: 0.5rem;
    background: white;
    border-radius: 6px;
}

.dimension .label {
    display: block;
    font-size: 0.875rem;
    color: #718096;
    margin-bottom: 0.25rem;
}

.dimension .value {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: #667eea;
}

/* Visual Preview */
.preview-container {
    text-align: center;
}

.aspect-preview {
    width: 100%;
    max-width: 200px;
    height: 120px;
    margin: 0 auto 1rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.preview-dimensions {
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.preview-info p {
    color: #718096;
    font-size: 0.875rem;
}

/* Use Cases */
.use-cases {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.use-case {
    padding: 0.75rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.use-case:hover {
    background: rgba(102, 126, 234, 0.2);
}

/* Coming Soon */
.coming-soon {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.coming-soon i {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.coming-soon h3 {
    color: #4a5568;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.coming-soon p {
    color: #718096;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    .calculator-grid {
        grid-template-columns: 1fr;
    }
    
    .dimension-inputs {
        grid-template-columns: 1fr;
    }
    
    .dimensions {
        grid-template-columns: 1fr;
    }
    
    .ratio-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
}
